import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsBoolean, <PERSON><PERSON><PERSON>al, IsA<PERSON>y, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AgentType } from '@prisma/client';

export class CreateAgentDto {
  @ApiProperty({ example: 'Customer Support Assistant' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'Helps customers with product questions' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ enum: AgentType, example: 'STANDALONE' })
  @IsEnum(AgentType)
  type: AgentType;

  @ApiProperty({ example: 'You are a helpful customer support assistant...' })
  @IsString()
  systemPrompt: string;

  @ApiProperty({ example: 'gpt-4' })
  @IsString()
  model: string;

  @ApiProperty({ example: 'openai' })
  @IsString()
  provider: string;

  @ApiProperty({ example: 0.7, minimum: 0, maximum: 2 })
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature: number;

  @ApiProperty({ example: 2048, minimum: 1, maximum: 8192 })
  @IsNumber()
  @Min(1)
  @Max(8192)
  maxTokens: number;

  @ApiProperty({ example: ['customer_service', 'product_knowledge'] })
  @IsOptional()
  @IsArray()
  skills?: string[];

  @ApiProperty({ example: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateAgentDto {
  @ApiProperty({ example: 'Updated Agent Name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ example: 'Updated description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ enum: AgentType })
  @IsOptional()
  @IsEnum(AgentType)
  type?: AgentType;

  @ApiProperty({ example: 'Updated system prompt...' })
  @IsOptional()
  @IsString()
  systemPrompt?: string;

  @ApiProperty({ example: 'gpt-4-turbo' })
  @IsOptional()
  @IsString()
  model?: string;

  @ApiProperty({ example: 'openai' })
  @IsOptional()
  @IsString()
  provider?: string;

  @ApiProperty({ minimum: 0, maximum: 2 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiProperty({ minimum: 1, maximum: 8192 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(8192)
  maxTokens?: number;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  skills?: string[];

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class AgentChatDto {
  @ApiProperty({ example: 'Hello, I need help with my order' })
  @IsString()
  message: string;

  @ApiProperty({ example: 'session_123', required: false })
  @IsOptional()
  @IsString()
  sessionId?: string;
}