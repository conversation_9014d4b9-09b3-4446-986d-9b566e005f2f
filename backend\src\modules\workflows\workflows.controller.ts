import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { WorkflowsService } from './workflows.service';
import { CreateWorkflowDto, UpdateWorkflowDto, ExecuteWorkflowDto } from './dto/workflow.dto';
import { AuthGuard } from '../../common/guards/auth.guard';

@ApiTags('Workflows')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('workflows')
export class WorkflowsController {
  constructor(private readonly workflowsService: WorkflowsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new workflow' })
  @ApiResponse({ status: 201, description: 'Workflow created successfully' })
  create(@Body() createWorkflowDto: CreateWorkflowDto, @Request() req) {
    return this.workflowsService.create(createWorkflowDto, req.user.organizationId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all workflows for organization' })
  @ApiResponse({ status: 200, description: 'List of workflows' })
  findAll(@Request() req) {
    return this.workflowsService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get workflow by ID' })
  @ApiResponse({ status: 200, description: 'Workflow details' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.workflowsService.findOne(id, req.user.organizationId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update workflow' })
  @ApiResponse({ status: 200, description: 'Workflow updated successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  update(
    @Param('id') id: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
    @Request() req,
  ) {
    return this.workflowsService.update(id, updateWorkflowDto, req.user.organizationId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete workflow' })
  @ApiResponse({ status: 200, description: 'Workflow deleted successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  remove(@Param('id') id: string, @Request() req) {
    return this.workflowsService.remove(id, req.user.organizationId);
  }

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute workflow' })
  @ApiResponse({ status: 200, description: 'Workflow executed successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  execute(
    @Param('id') id: string,
    @Body() executeDto: ExecuteWorkflowDto,
    @Request() req,
  ) {
    return this.workflowsService.execute(id, executeDto, req.user.organizationId, req.user.id);
  }
}