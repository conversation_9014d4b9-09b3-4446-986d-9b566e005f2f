import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';
import { ApixService } from '../apix/apix.service';
import { CreateToolDto, UpdateToolDto, ExecuteToolDto } from './dto/tool.dto';
import axios from 'axios';

@Injectable()
export class ToolsService {
  constructor(
    private prisma: PrismaService,
    private apixService: ApixService,
  ) {}

  async create(createToolDto: CreateToolDto, organizationId: string) {
    const tool = await this.prisma.tool.create({
      data: {
        ...createToolDto,
        organizationId,
      },
    });

    this.apixService.emitToOrganization(organizationId, 'tool-created', {
      toolId: tool.id,
      name: tool.name,
      type: tool.type,
    });

    return tool;
  }

  async findAll(organizationId: string) {
    return this.prisma.tool.findMany({
      where: { organizationId },
      include: {
        executions: {
          take: 5,
          orderBy: { createdAt: 'desc' },
        },
        agentConnections: {
          include: { agent: true },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });
  }

  async findOne(id: string, organizationId: string) {
    const tool = await this.prisma.tool.findFirst({
      where: { id, organizationId },
      include: {
        executions: {
          orderBy: { createdAt: 'desc' },
          take: 20,
        },
        agentConnections: {
          include: { agent: true },
        },
      },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    return tool;
  }

  async update(id: string, updateToolDto: UpdateToolDto, organizationId: string) {
    const tool = await this.prisma.tool.updateMany({
      where: { id, organizationId },
      data: updateToolDto,
    });

    if (tool.count === 0) {
      throw new NotFoundException('Tool not found');
    }

    const updatedTool = await this.findOne(id, organizationId);

    this.apixService.emitToOrganization(organizationId, 'tool-updated', {
      toolId: id,
      changes: updateToolDto,
    });

    return updatedTool;
  }

  async remove(id: string, organizationId: string) {
    const tool = await this.prisma.tool.deleteMany({
      where: { id, organizationId },
    });

    if (tool.count === 0) {
      throw new NotFoundException('Tool not found');
    }

    this.apixService.emitToOrganization(organizationId, 'tool-deleted', {
      toolId: id,
    });

    return { deleted: true };
  }

  async execute(id: string, executeDto: ExecuteToolDto, organizationId: string) {
    const tool = await this.findOne(id, organizationId);

    const execution = await this.prisma.toolExecution.create({
      data: {
        toolId: id,
        input: executeDto.input,
        status: 'RUNNING',
        sessionId: executeDto.sessionId,
      },
    });

    this.apixService.emitToOrganization(organizationId, 'tool-execution-started', {
      executionId: execution.id,
      toolId: id,
    });

    try {
      let result;
      const startTime = Date.now();

      switch (tool.type) {
        case 'FUNCTION_CALL':
          result = await this.executeFunctionCall(tool, executeDto.input);
          break;
        case 'REST_API':
          result = await this.executeRestApi(tool, executeDto.input);
          break;
        case 'RAG_RETRIEVAL':
          result = await this.executeRagRetrieval(tool, executeDto.input);
          break;
        default:
          throw new Error(`Unsupported tool type: ${tool.type}`);
      }

      const duration = Date.now() - startTime;

      const completedExecution = await this.prisma.toolExecution.update({
        where: { id: execution.id },
        data: {
          output: result,
          status: 'COMPLETED',
          duration,
          completedAt: new Date(),
        },
      });

      this.apixService.emitToOrganization(organizationId, 'tool-execution-completed', {
        executionId: execution.id,
        toolId: id,
        result,
        duration,
      });

      return completedExecution;
    } catch (error) {
      await this.prisma.toolExecution.update({
        where: { id: execution.id },
        data: {
          status: 'FAILED',
          error: error.message,
          completedAt: new Date(),
        },
      });

      this.apixService.emitToOrganization(organizationId, 'tool-execution-failed', {
        executionId: execution.id,
        toolId: id,
        error: error.message,
      });

      throw error;
    }
  }

  private async executeFunctionCall(tool: any, input: any) {
    // Execute function call based on tool configuration
    const { functionName, parameters } = tool.config;
    
    // This is a simplified implementation
    // In a real system, you'd have a function registry
    switch (functionName) {
      case 'calculate':
        return this.calculateFunction(input);
      case 'format_text':
        return this.formatTextFunction(input);
      default:
        throw new Error(`Unknown function: ${functionName}`);
    }
  }

  private async executeRestApi(tool: any, input: any) {
    const { url, method, headers } = tool.config;
    
    const response = await axios({
      url,
      method: method || 'GET',
      headers: headers || {},
      data: method !== 'GET' ? input : undefined,
      params: method === 'GET' ? input : undefined,
    });

    return response.data;
  }

  private async executeRagRetrieval(tool: any, input: any) {
    const { query } = input;
    
    // Simplified RAG implementation
    // In a real system, you'd use vector similarity search
    const documents = await this.prisma.document.findMany({
      where: {
        knowledgeBase: {
          organizationId: tool.organizationId,
        },
        content: {
          contains: query,
          mode: 'insensitive',
        },
      },
      take: 5,
    });

    return {
      query,
      results: documents.map(doc => ({
        id: doc.id,
        content: doc.content.substring(0, 500),
        relevance: Math.random(), // Simplified relevance scoring
      })),
    };
  }

  private calculateFunction(input: any) {
    const { operation, a, b } = input;
    
    switch (operation) {
      case 'add':
        return { result: a + b };
      case 'subtract':
        return { result: a - b };
      case 'multiply':
        return { result: a * b };
      case 'divide':
        return { result: b !== 0 ? a / b : 'Error: Division by zero' };
      default:
        throw new Error(`Unknown operation: ${operation}`);
    }
  }

  private formatTextFunction(input: any) {
    const { text, format } = input;
    
    switch (format) {
      case 'uppercase':
        return { result: text.toUpperCase() };
      case 'lowercase':
        return { result: text.toLowerCase() };
      case 'title_case':
        return { result: text.replace(/\w\S*/g, (txt) => 
          txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()) };
      default:
        return { result: text };
    }
  }
}