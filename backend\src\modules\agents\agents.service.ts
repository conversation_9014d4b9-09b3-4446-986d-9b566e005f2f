import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';
import { ProvidersService } from '../providers/providers.service';
import { ApixService } from '../apix/apix.service';
import { CreateAgentDto, UpdateAgentDto, AgentChatDto } from './dto/agent.dto';

@Injectable()
export class AgentsService {
  constructor(
    private prisma: PrismaService,
    private providersService: ProvidersService,
    private apixService: ApixService,
  ) {}

  async create(createAgentDto: CreateAgentDto, organizationId: string) {
    const agent = await this.prisma.agent.create({
      data: {
        ...createAgentDto,
        organizationId,
      },
      include: {
        toolConnections: {
          include: { tool: true },
        },
      },
    });

    this.apixService.emitToOrganization(organizationId, 'agent-created', {
      agentId: agent.id,
      name: agent.name,
    });

    return agent;
  }

  async findAll(organizationId: string) {
    return this.prisma.agent.findMany({
      where: { organizationId },
      include: {
        toolConnections: {
          include: { tool: true },
        },
        sessions: {
          where: { status: 'ACTIVE' },
          take: 5,
          orderBy: { updatedAt: 'desc' },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });
  }

  async findOne(id: string, organizationId: string) {
    const agent = await this.prisma.agent.findFirst({
      where: { id, organizationId },
      include: {
        toolConnections: {
          include: { tool: true },
        },
        sessions: {
          include: { messages: true },
          orderBy: { updatedAt: 'desc' },
        },
      },
    });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    return agent;
  }

  async update(id: string, updateAgentDto: UpdateAgentDto, organizationId: string) {
    const agent = await this.prisma.agent.updateMany({
      where: { id, organizationId },
      data: updateAgentDto,
    });

    if (agent.count === 0) {
      throw new NotFoundException('Agent not found');
    }

    const updatedAgent = await this.findOne(id, organizationId);

    this.apixService.emitToOrganization(organizationId, 'agent-updated', {
      agentId: id,
      changes: updateAgentDto,
    });

    return updatedAgent;
  }

  async remove(id: string, organizationId: string) {
    const agent = await this.prisma.agent.deleteMany({
      where: { id, organizationId },
    });

    if (agent.count === 0) {
      throw new NotFoundException('Agent not found');
    }

    this.apixService.emitToOrganization(organizationId, 'agent-deleted', {
      agentId: id,
    });

    return { deleted: true };
  }

  async chat(id: string, chatDto: AgentChatDto, organizationId: string, userId: string) {
    const agent = await this.findOne(id, organizationId);
    
    let session = chatDto.sessionId 
      ? await this.prisma.session.findFirst({
          where: { id: chatDto.sessionId, organizationId },
          include: { messages: true },
        })
      : null;

    if (!session) {
      session = await this.prisma.session.create({
        data: {
          name: `Chat with ${agent.name}`,
          organizationId,
          agentId: id,
          userId,
          status: 'ACTIVE',
        },
        include: { messages: true },
      });
    }

    // Store user message
    const userMessage = await this.prisma.message.create({
      data: {
        role: 'USER',
        content: chatDto.message,
        sessionId: session.id,
      },
    });

    this.apixService.emitToSession(session.id, 'message-received', {
      messageId: userMessage.id,
      role: 'USER',
      content: chatDto.message,
    });

    // Generate AI response using the enhanced providers service
    const response = await this.providersService.generateResponse({
      provider: agent.provider,
      model: agent.model,
      messages: [
        { role: 'system', content: agent.systemPrompt },
        ...session.messages.map(m => ({ role: m.role.toLowerCase(), content: m.content })),
        { role: 'user', content: chatDto.message },
      ],
      temperature: agent.temperature,
      maxTokens: agent.maxTokens,
      organizationId,
    });

    // Store AI response
    const assistantMessage = await this.prisma.message.create({
      data: {
        role: 'ASSISTANT',
        content: response.content,
        sessionId: session.id,
        metadata: {
          provider: response.provider,
          model: response.model,
          tokenUsage: response.tokenUsage,
          latency: response.latency,
          cost: response.cost,
        },
      },
    });

    this.apixService.emitToSession(session.id, 'message-generated', {
      messageId: assistantMessage.id,
      role: 'ASSISTANT',
      content: response.content,
      metadata: {
        provider: response.provider,
        latency: response.latency,
        cost: response.cost,
      },
    });

    return {
      sessionId: session.id,
      userMessage,
      assistantMessage,
      response: response.content,
      metadata: {
        provider: response.provider,
        model: response.model,
        latency: response.latency,
        cost: response.cost,
        tokenUsage: response.tokenUsage,
      },
    };
  }
}