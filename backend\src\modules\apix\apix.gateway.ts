import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  WebSocketServer,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { UseGuards } from '@nestjs/common';
import { ApixService } from './apix.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/apix',
})
export class ApixGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  constructor(private apixService: ApixService) {}

  async handleConnection(client: Socket) {
    try {
      const user = await this.apixService.authenticateSocket(client);
      client.data.user = user;
      client.join(`org:${user.organizationId}`);
      
      this.apixService.emitEvent('connection', {
        userId: user.id,
        organizationId: user.organizationId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    if (client.data.user) {
      this.apixService.emitEvent('disconnection', {
        userId: client.data.user.id,
        organizationId: client.data.user.organizationId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('join-session')
  @UseGuards(JwtAuthGuard)
  async joinSession(
    @MessageBody() data: { sessionId: string },
    @ConnectedSocket() client: Socket,
  ) {
    await client.join(`session:${data.sessionId}`);
    return { status: 'joined', sessionId: data.sessionId };
  }

  @SubscribeMessage('leave-session')
  @UseGuards(JwtAuthGuard)
  async leaveSession(
    @MessageBody() data: { sessionId: string },
    @ConnectedSocket() client: Socket,
  ) {
    await client.leave(`session:${data.sessionId}`);
    return { status: 'left', sessionId: data.sessionId };
  }

  @SubscribeMessage('agent-message')
  @UseGuards(JwtAuthGuard)
  async handleAgentMessage(
    @MessageBody() data: any,
    @ConnectedSocket() client: Socket,
  ) {
    const user = client.data.user;
    await this.apixService.handleAgentMessage(data, user);
    return { status: 'processed' };
  }
}