import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';
import { ApixService } from '../apix/apix.service';

@Injectable()
export class SessionsService {
  constructor(
    private prisma: PrismaService,
    private apixService: ApixService,
  ) {}

  async findAll(organizationId: string) {
    return this.prisma.session.findMany({
      where: { organizationId },
      include: {
        user: true,
        agent: true,
        messages: {
          take: 5,
          orderBy: { createdAt: 'desc' },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });
  }

  async findOne(id: string, organizationId: string) {
    const session = await this.prisma.session.findFirst({
      where: { id, organizationId },
      include: {
        user: true,
        agent: true,
        messages: {
          orderBy: { createdAt: 'asc' },
        },
      },
    });

    if (!session) {
      throw new NotFoundException('Session not found');
    }

    return session;
  }

  async remove(id: string, organizationId: string) {
    const session = await this.prisma.session.deleteMany({
      where: { id, organizationId },
    });

    if (session.count === 0) {
      throw new NotFoundException('Session not found');
    }

    this.apixService.emitToOrganization(organizationId, 'session-deleted', {
      sessionId: id,
    });

    return { deleted: true };
  }
}