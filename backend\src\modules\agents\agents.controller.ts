import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AgentsService } from './agents.service';
import { CreateAgentDto, UpdateAgentDto, AgentChatDto } from './dto/agent.dto';
import { AuthGuard } from '../../common/guards/auth.guard';

@ApiTags('Agents')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('agents')
export class AgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new agent' })
  @ApiResponse({ status: 201, description: 'Agent created successfully' })
  create(@Body() createAgentDto: CreateAgentDto, @Request() req) {
    return this.agentsService.create(createAgentDto, req.user.organizationId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all agents for organization' })
  @ApiResponse({ status: 200, description: 'List of agents' })
  findAll(@Request() req) {
    return this.agentsService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get agent by ID' })
  @ApiResponse({ status: 200, description: 'Agent details' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.agentsService.findOne(id, req.user.organizationId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update agent' })
  @ApiResponse({ status: 200, description: 'Agent updated successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  update(
    @Param('id') id: string,
    @Body() updateAgentDto: UpdateAgentDto,
    @Request() req,
  ) {
    return this.agentsService.update(id, updateAgentDto, req.user.organizationId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete agent' })
  @ApiResponse({ status: 200, description: 'Agent deleted successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  remove(@Param('id') id: string, @Request() req) {
    return this.agentsService.remove(id, req.user.organizationId);
  }

  @Post(':id/chat')
  @ApiOperation({ summary: 'Chat with agent' })
  @ApiResponse({ status: 200, description: 'Chat response generated' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  chat(
    @Param('id') id: string,
    @Body() chatDto: AgentChatDto,
    @Request() req,
  ) {
    return this.agentsService.chat(id, chatDto, req.user.organizationId, req.user.id);
  }
}