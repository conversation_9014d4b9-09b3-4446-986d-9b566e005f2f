import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { GitBranch } from 'lucide-react'

export const ConditionNode = memo(({ data, selected }: NodeProps) => {
  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''} border-orange-200`}>
      <CardContent className="p-3">
        <div className="flex items-center gap-2 mb-2">
          <GitBranch className="h-4 w-4 text-orange-500" />
          <span className="font-medium text-sm">{data.label || 'Condition'}</span>
        </div>
        
        <Badge variant="outline" className="text-xs">
          Logic Node
        </Badge>
        
        {data.condition && (
          <p className="text-xs text-muted-foreground mt-2 font-mono">
            {data.condition}
          </p>
        )}
        
        <div className="flex gap-1 mt-2">
          <Badge variant="default" className="text-xs bg-green-500">
            {data.trueLabel || 'True'}
          </Badge>
          <Badge variant="destructive" className="text-xs">
            {data.falseLabel || 'False'}
          </Badge>
        </div>
      </CardContent>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} id="true" className="w-3 h-3" style={{ top: '30%' }} />
      <Handle type="source" position={Position.Right} id="false" className="w-3 h-3" style={{ top: '70%' }} />
    </Card>
  )
})