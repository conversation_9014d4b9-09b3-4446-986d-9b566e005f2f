'use client'

import { useQuery } from 'react-query'
import { useParams } from 'next/navigation'
import { AgentDetails } from '@/components/agents/agent-details'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { api } from '@/lib/api'

export default function AgentDetailPage() {
  const params = useParams()
  const agentId = params.id as string

  const { data: agent, isLoading } = useQuery(['agent', agentId], () =>
    api.get(`/agents/${agentId}`).then(res => res.data)
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    )
  }

  if (!agent) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold mb-2">Agent not found</h3>
        <p className="text-muted-foreground">
          The agent you're looking for doesn't exist or has been deleted.
        </p>
      </div>
    )
  }

  return <AgentDetails agent={agent} />
}