'use client'

import { useQuery } from 'react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Overview } from '@/components/dashboard/overview'
import { MetricCard } from '@/components/dashboard/metric-card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { api } from '@/lib/api'
import { BarChart3, TrendingUp, Activity, Clock, Zap, AlertCircle } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

export default function AnalyticsPage() {
  const { data: metrics, isLoading } = useQuery('analytics-detailed', () =>
    api.get('/analytics/dashboard').then(res => res.data)
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    )
  }

  const overview = metrics?.overview || {}
  const recentActivity = metrics?.recentActivity || []
  const trends = metrics?.trends || []

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics</h1>
          <p className="text-muted-foreground">
            Monitor performance and usage across your AI platform
          </p>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Executions"
          value={overview.totalExecutions || 0}
          description="All-time tool executions"
          icon={Activity}
          trend="+12% from last month"
        />
        <MetricCard
          title="Success Rate"
          value="94.2%"
          description="Successful executions"
          icon={TrendingUp}
          trend="+2.1% from last week"
        />
        <MetricCard
          title="Avg Response Time"
          value="1.2s"
          description="Average execution time"
          icon={Clock}
          trend="-0.3s improvement"
        />
        <MetricCard
          title="Active Agents"
          value={overview.agents || 0}
          description="Currently deployed"
          icon={Zap}
          trend="Real-time"
        />
      </div>

      {/* Charts and Activity */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Execution Trends
            </CardTitle>
            <CardDescription>
              Daily execution volume over the last 30 days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Overview data={trends} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest tool executions and events
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentActivity.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground text-sm">No recent activity</p>
              </div>
            ) : (
              <div className="space-y-3">
                {recentActivity.slice(0, 8).map((activity: any) => (
                  <div key={activity.id} className="flex items-center justify-between text-sm">
                    <div className="flex-1">
                      <p className="font-medium">{activity.tool?.name || 'Unknown Tool'}</p>
                      <p className="text-muted-foreground text-xs">
                        {new Date(activity.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge 
                      variant={
                        activity.status === 'COMPLETED' ? 'default' : 
                        activity.status === 'FAILED' ? 'destructive' : 'secondary'
                      }
                      className="text-xs"
                    >
                      {activity.status}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Performance Insights */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Performance Insights</CardTitle>
            <CardDescription>
              Key performance indicators and recommendations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start gap-3">
              <TrendingUp className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <p className="font-medium text-sm">Execution Success Rate Up</p>
                <p className="text-muted-foreground text-xs">
                  Success rate improved by 2.1% this week
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Clock className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <p className="font-medium text-sm">Response Time Optimized</p>
                <p className="text-muted-foreground text-xs">
                  Average response time reduced by 300ms
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5" />
              <div>
                <p className="font-medium text-sm">Resource Usage Alert</p>
                <p className="text-muted-foreground text-xs">
                  Consider scaling up for peak hours
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Usage Breakdown</CardTitle>
            <CardDescription>
              Tool and agent usage distribution
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Function Calls</span>
                <span className="font-medium">45%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45%' }}></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>REST API Calls</span>
                <span className="font-medium">30%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '30%' }}></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>RAG Retrieval</span>
                <span className="font-medium">25%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-purple-500 h-2 rounded-full" style={{ width: '25%' }}></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}