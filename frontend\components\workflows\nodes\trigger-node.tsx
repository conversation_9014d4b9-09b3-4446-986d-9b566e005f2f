import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Zap } from 'lucide-react'

export const TriggerNode = memo(({ data, selected }: NodeProps) => {
  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''} border-green-200`}>
      <CardContent className="p-3">
        <div className="flex items-center gap-2 mb-2">
          <Zap className="h-4 w-4 text-yellow-500" />
          <span className="font-medium text-sm">{data.label || 'Trigger'}</span>
        </div>
        
        <Badge variant="default" className="text-xs bg-green-500">
          Entry Point
        </Badge>
        
        <p className="text-xs text-muted-foreground mt-2">
          {data.triggerType || 'Webhook'} trigger
        </p>
      </CardContent>
      
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </Card>
  )
})