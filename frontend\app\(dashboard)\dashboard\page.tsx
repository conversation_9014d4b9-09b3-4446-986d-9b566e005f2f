'use client'

import { useQuery } from 'react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Overview } from '@/components/dashboard/overview'
import { RecentActivity } from '@/components/dashboard/recent-activity'
import { MetricCard } from '@/components/dashboard/metric-card'
import { api } from '@/lib/api'
import { Brain, Wrench, Workflow, Activity } from 'lucide-react'

export default function DashboardPage() {
  const { data: metrics, isLoading } = useQuery('dashboard-metrics', () =>
    api.get('/analytics/dashboard').then(res => res.data)
  )

  const { data: stats } = useQuery('organization-stats', () =>
    api.get('/organizations/stats').then(res => res.data)
  )

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Dashboard</h1>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const overview = metrics?.overview || stats || {}

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Dashboard</h1>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Agents"
          value={overview.agents || 0}
          description="Active AI agents"
          icon={Brain}
          trend="+2 from last month"
        />
        <MetricCard
          title="Tools"
          value={overview.tools || 0}
          description="Available tools"
          icon={Wrench}
          trend="+5 from last month"
        />
        <MetricCard
          title="Workflows"
          value={overview.workflows || 0}
          description="Automated workflows"
          icon={Workflow}
          trend="+1 from last month"
        />
        <MetricCard
          title="Active Sessions"
          value={overview.activeSessions || 0}
          description="Live conversations"
          icon={Activity}
          trend="Real-time"
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Overview</CardTitle>
          </CardHeader>
          <CardContent className="pl-2">
            <Overview data={metrics?.trends || []} />
          </CardContent>
        </Card>
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest executions and events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RecentActivity data={metrics?.recentActivity || []} />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}