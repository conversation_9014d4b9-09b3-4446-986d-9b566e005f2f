import { Module } from '@nestjs/common';
import { ToolsService } from './tools.service';
import { ToolsController } from './tools.controller';
import { PrismaModule } from '../../common/prisma/prisma.module';
import { ApixModule } from '../apix/apix.module';

@Module({
  imports: [PrismaModule, ApixModule],
  controllers: [ToolsController],
  providers: [ToolsService],
  exports: [ToolsService],
})
export class ToolsModule {}