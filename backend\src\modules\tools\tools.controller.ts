import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ToolsService } from './tools.service';
import { CreateToolDto, UpdateToolDto, ExecuteToolDto } from './dto/tool.dto';
import { AuthGuard } from '../../common/guards/auth.guard';

@ApiTags('Tools')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('tools')
export class ToolsController {
  constructor(private readonly toolsService: ToolsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new tool' })
  @ApiResponse({ status: 201, description: 'Tool created successfully' })
  create(@Body() createToolDto: CreateToolDto, @Request() req) {
    return this.toolsService.create(createToolDto, req.user.organizationId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all tools for organization' })
  @ApiResponse({ status: 200, description: 'List of tools' })
  findAll(@Request() req) {
    return this.toolsService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get tool by ID' })
  @ApiResponse({ status: 200, description: 'Tool details' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.toolsService.findOne(id, req.user.organizationId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update tool' })
  @ApiResponse({ status: 200, description: 'Tool updated successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  update(
    @Param('id') id: string,
    @Body() updateToolDto: UpdateToolDto,
    @Request() req,
  ) {
    return this.toolsService.update(id, updateToolDto, req.user.organizationId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete tool' })
  @ApiResponse({ status: 200, description: 'Tool deleted successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  remove(@Param('id') id: string, @Request() req) {
    return this.toolsService.remove(id, req.user.organizationId);
  }

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute tool' })
  @ApiResponse({ status: 200, description: 'Tool executed successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  execute(
    @Param('id') id: string,
    @Body() executeDto: ExecuteToolDto,
    @Request() req,
  ) {
    return this.toolsService.execute(id, executeDto, req.user.organizationId);
  }
}