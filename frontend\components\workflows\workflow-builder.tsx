'use client'

import { use<PERSON><PERSON><PERSON>, useEffect, useState } from 'react'
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  Panel,
} from 'reactflow'
import 'reactflow/dist/style.css'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'
import {
  Play,
  Save,
  Plus,
  Settings,
  Trash2,
  Copy,
  Download,
  Upload,
  Zap,
  Bot,
  Wrench,
  GitBranch,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react'

// Custom Node Components
import { AgentNode } from './nodes/agent-node'
import { ToolNode } from './nodes/tool-node'
import { TriggerNode } from './nodes/trigger-node'
import { ConditionNode } from './nodes/condition-node'
import { OutputNode } from './nodes/output-node'
import { DelayNode } from './nodes/delay-node'

const nodeTypes = {
  agent: AgentNode,
  tool: ToolNode,
  trigger: TriggerNode,
  condition: ConditionNode,
  output: OutputNode,
  delay: DelayNode,
}

interface WorkflowBuilderProps {
  workflowId?: string
  onSave?: (workflow: any) => void
}

const defaultWorkflow = {
  nodes: [
    {
      id: 'trigger-1',
      type: 'trigger',
      position: { x: 100, y: 100 },
      data: {
        label: 'Webhook Trigger',
        triggerType: 'webhook',
        config: {},
      },
    },
  ],
  edges: [],
}

export function WorkflowBuilder({ workflowId, onSave }: WorkflowBuilderProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState(defaultWorkflow.nodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(defaultWorkflow.edges)
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const [workflowName, setWorkflowName] = useState('Untitled Workflow')
  const [workflowDescription, setWorkflowDescription] = useState('')
  const [isExecuting, setIsExecuting] = useState(false)
  const [executionResults, setExecutionResults] = useState<any[]>([])

  const queryClient = useQueryClient()

  // Load existing workflow if editing
  const { data: workflow } = useQuery(
    ['workflow', workflowId],
    () => api.get(`/workflows/${workflowId}`).then(res => res.data),
    { enabled: !!workflowId }
  )

  // Load agents and tools for node creation
  const { data: agents } = useQuery('agents', () =>
    api.get('/agents').then(res => res.data)
  )

  const { data: tools } = useQuery('tools', () =>
    api.get('/tools').then(res => res.data)
  )

  // Initialize workflow data
  useEffect(() => {
    if (workflow) {
      setWorkflowName(workflow.name)
      setWorkflowDescription(workflow.description || '')
      if (workflow.definition?.nodes) {
        setNodes(workflow.definition.nodes)
      }
      if (workflow.definition?.edges) {
        setEdges(workflow.definition.edges)
      }
    }
  }, [workflow, setNodes, setEdges])

  const onConnect = useCallback(
    (connection: Connection) => setEdges((eds) => addEdge(connection, eds)),
    [setEdges]
  )

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node)
  }, [])

  const onPaneClick = useCallback(() => {
    setSelectedNode(null)
  }, [])

  // Save workflow mutation
  const saveWorkflowMutation = useMutation(
    (workflowData: any) => {
      if (workflowId) {
        return api.patch(`/workflows/${workflowId}`, workflowData)
      } else {
        return api.post('/workflows', workflowData)
      }
    },
    {
      onSuccess: (response) => {
        toast.success('Workflow saved successfully!')
        if (onSave) {
          onSave(response.data)
        }
        queryClient.invalidateQueries('workflows')
      },
      onError: () => {
        toast.error('Failed to save workflow')
      },
    }
  )

  // Execute workflow mutation
  const executeWorkflowMutation = useMutation(
    (input: any) => {
      const id = workflowId || workflow?.id
      if (!id) throw new Error('No workflow ID')
      return api.post(`/workflows/${id}/execute`, { input })
    },
    {
      onSuccess: (response) => {
        toast.success('Workflow executed successfully!')
        setExecutionResults(prev => [response.data, ...prev])
      },
      onError: () => {
        toast.error('Failed to execute workflow')
      },
    }
  )

  const handleSave = () => {
    const workflowData = {
      name: workflowName,
      description: workflowDescription,
      definition: {
        nodes,
        edges,
        viewport: { x: 0, y: 0, zoom: 1 },
      },
    }
    saveWorkflowMutation.mutate(workflowData)
  }

  const handleExecute = () => {
    const input = { message: 'Test execution', timestamp: new Date().toISOString() }
    setIsExecuting(true)
    executeWorkflowMutation.mutate(input)
    setTimeout(() => setIsExecuting(false), 2000) // Reset after 2 seconds
  }

  const addNode = (type: string) => {
    const nodeId = `${type}-${Date.now()}`
    const newNode: Node = {
      id: nodeId,
      type,
      position: { x: 300, y: 200 },
      data: getDefaultNodeData(type),
    }
    setNodes((nds) => [...nds, newNode])
  }

  const getDefaultNodeData = (type: string) => {
    switch (type) {
      case 'agent':
        return {
          label: 'AI Agent',
          agentId: '',
          config: {},
        }
      case 'tool':
        return {
          label: 'Tool Execution',
          toolId: '',
          config: {},
        }
      case 'condition':
        return {
          label: 'Condition',
          condition: 'input.value > 0',
          trueLabel: 'True',
          falseLabel: 'False',
        }
      case 'delay':
        return {
          label: 'Delay',
          duration: 1000,
          unit: 'ms',
        }
      case 'output':
        return {
          label: 'Output',
          format: 'json',
          template: '{{ $json }}',
        }
      default:
        return { label: 'Node' }
    }
  }

  const updateNodeData = (nodeId: string, newData: any) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          return { ...node, data: { ...node.data, ...newData } }
        }
        return node
      })
    )
  }

  const deleteSelectedNode = () => {
    if (selectedNode) {
      setNodes((nds) => nds.filter((node) => node.id !== selectedNode.id))
      setEdges((eds) => eds.filter((edge) => 
        edge.source !== selectedNode.id && edge.target !== selectedNode.id
      ))
      setSelectedNode(null)
    }
  }

  const duplicateSelectedNode = () => {
    if (selectedNode) {
      const newNode: Node = {
        ...selectedNode,
        id: `${selectedNode.type}-${Date.now()}`,
        position: {
          x: selectedNode.position.x + 50,
          y: selectedNode.position.y + 50,
        },
      }
      setNodes((nds) => [...nds, newNode])
    }
  }

  const exportWorkflow = () => {
    const workflowData = {
      name: workflowName,
      description: workflowDescription,
      definition: { nodes, edges },
      version: '1.0.0',
      exportedAt: new Date().toISOString(),
    }
    
    const dataStr = JSON.stringify(workflowData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${workflowName.replace(/\s+/g, '-').toLowerCase()}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  const importWorkflow = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const imported = JSON.parse(e.target?.result as string)
          setWorkflowName(imported.name || 'Imported Workflow')
          setWorkflowDescription(imported.description || '')
          setNodes(imported.definition.nodes || [])
          setEdges(imported.definition.edges || [])
          toast.success('Workflow imported successfully!')
        } catch (error) {
          toast.error('Failed to import workflow')
        }
      }
      reader.readAsText(file)
    }
  }

  return (
    <div className="h-screen flex">
      {/* Left Sidebar - Node Palette */}
      <div className="w-80 border-r bg-background flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-2">Workflow Builder</h2>
          <div className="space-y-2">
            <Input
              placeholder="Workflow name"
              value={workflowName}
              onChange={(e) => setWorkflowName(e.target.value)}
            />
            <Textarea
              placeholder="Description (optional)"
              value={workflowDescription}
              onChange={(e) => setWorkflowDescription(e.target.value)}
              rows={2}
            />
          </div>
        </div>

        <Tabs defaultValue="nodes" className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-3 mx-4 mt-2">
            <TabsTrigger value="nodes">Nodes</TabsTrigger>
            <TabsTrigger value="execution">Execution</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="nodes" className="flex-1 p-4 space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Triggers</h3>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  className="justify-start h-12"
                  onClick={() => addNode('trigger')}
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Webhook Trigger
                </Button>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-sm font-medium mb-2">AI Agents</h3>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  className="justify-start h-12"
                  onClick={() => addNode('agent')}
                >
                  <Bot className="h-4 w-4 mr-2" />
                  AI Agent
                </Button>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-sm font-medium mb-2">Tools</h3>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  className="justify-start h-12"
                  onClick={() => addNode('tool')}
                >
                  <Wrench className="h-4 w-4 mr-2" />
                  Tool Execution
                </Button>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-sm font-medium mb-2">Logic</h3>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  className="justify-start h-12"
                  onClick={() => addNode('condition')}
                >
                  <GitBranch className="h-4 w-4 mr-2" />
                  Condition
                </Button>
                <Button
                  variant="outline"
                  className="justify-start h-12"
                  onClick={() => addNode('delay')}
                >
                  <Clock className="h-4 w-4 mr-2" />
                  Delay
                </Button>
                <Button
                  variant="outline"
                  className="justify-start h-12"
                  onClick={() => addNode('output')}
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Output
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="execution" className="flex-1 p-4">
            <div className="space-y-4">
              <div className="flex gap-2">
                <Button onClick={handleExecute} disabled={isExecuting} className="flex-1">
                  <Play className="h-4 w-4 mr-2" />
                  {isExecuting ? 'Running...' : 'Test Run'}
                </Button>
              </div>

              {executionResults.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium mb-2">Execution History</h3>
                  <ScrollArea className="h-64">
                    <div className="space-y-2">
                      {executionResults.map((result, index) => (
                        <Card key={index}>
                          <CardContent className="p-3">
                            <div className="flex items-center justify-between mb-2">
                              <Badge variant={result.status === 'COMPLETED' ? 'default' : 'destructive'}>
                                {result.status}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {new Date(result.createdAt).toLocaleTimeString()}
                              </span>
                            </div>
                            <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                              {JSON.stringify(result.output, null, 2)}
                            </pre>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="settings" className="flex-1 p-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Auto-save</span>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Debug mode</span>
                <Switch />
              </div>
              <Separator />
              <div className="space-y-2">
                <Label>Export/Import</Label>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={exportWorkflow}>
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </Button>
                  <div className="relative">
                    <Button variant="outline" size="sm">
                      <Upload className="h-4 w-4 mr-1" />
                      Import
                    </Button>
                    <input
                      type="file"
                      accept=".json"
                      onChange={importWorkflow}
                      className="absolute inset-0 opacity-0 cursor-pointer"
                    />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Main Canvas */}
      <div className="flex-1 relative">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          onPaneClick={onPaneClick}
          nodeTypes={nodeTypes}
          fitView
          className="bg-gray-50 dark:bg-gray-900"
        >
          <Background variant={BackgroundVariant.Dots} />
          <Controls />
          <MiniMap />
          
          {/* Top Toolbar */}
          <Panel position="top-right">
            <div className="flex gap-2">
              <Button onClick={handleSave} disabled={saveWorkflowMutation.isLoading}>
                <Save className="h-4 w-4 mr-2" />
                {saveWorkflowMutation.isLoading ? 'Saving...' : 'Save'}
              </Button>
              <Button onClick={handleExecute} disabled={isExecuting}>
                <Play className="h-4 w-4 mr-2" />
                {isExecuting ? 'Running...' : 'Run'}
              </Button>
            </div>
          </Panel>

          {/* Node Actions */}
          {selectedNode && (
            <Panel position="top-left">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Node Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={duplicateSelectedNode}>
                      <Copy className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={deleteSelectedNode}>
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </Panel>
          )}
        </ReactFlow>
      </div>

      {/* Right Sidebar - Node Properties */}
      {selectedNode && (
        <div className="w-80 border-l bg-background">
          <NodePropertiesPanel
            node={selectedNode}
            agents={agents || []}
            tools={tools || []}
            onUpdateNode={updateNodeData}
          />
        </div>
      )}
    </div>
  )
}

// Node Properties Panel
interface NodePropertiesPanelProps {
  node: Node
  agents: any[]
  tools: any[]
  onUpdateNode: (nodeId: string, data: any) => void
}

function NodePropertiesPanel({ node, agents, tools, onUpdateNode }: NodePropertiesPanelProps) {
  const updateData = (key: string, value: any) => {
    onUpdateNode(node.id, { [key]: value })
  }

  const renderNodeSpecificFields = () => {
    switch (node.type) {
      case 'agent':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Agent</Label>
              <Select value={node.data.agentId} onValueChange={(value) => updateData('agentId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select agent" />
                </SelectTrigger>
                <SelectContent>
                  {agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id}>
                      {agent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Custom Instructions</Label>
              <Textarea
                placeholder="Additional instructions for this node..."
                value={node.data.instructions || ''}
                onChange={(e) => updateData('instructions', e.target.value)}
              />
            </div>
          </div>
        )
      
      case 'tool':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Tool</Label>
              <Select value={node.data.toolId} onValueChange={(value) => updateData('toolId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select tool" />
                </SelectTrigger>
                <SelectContent>
                  {tools.map((tool) => (
                    <SelectItem key={tool.id} value={tool.id}>
                      {tool.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Input Mapping</Label>
              <Textarea
                placeholder='{"param": "{{ $json.input }}"}'
                value={node.data.inputMapping || ''}
                onChange={(e) => updateData('inputMapping', e.target.value)}
              />
            </div>
          </div>
        )
      
      case 'condition':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Condition Expression</Label>
              <Input
                placeholder="input.value > 0"
                value={node.data.condition || ''}
                onChange={(e) => updateData('condition', e.target.value)}
              />
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-2">
                <Label>True Label</Label>
                <Input
                  placeholder="True"
                  value={node.data.trueLabel || ''}
                  onChange={(e) => updateData('trueLabel', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label>False Label</Label>
                <Input
                  placeholder="False"
                  value={node.data.falseLabel || ''}
                  onChange={(e) => updateData('falseLabel', e.target.value)}
                />
              </div>
            </div>
          </div>
        )
      
      case 'delay':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Duration</Label>
              <div className="flex gap-2">
                <Input
                  type="number"
                  placeholder="1000"
                  value={node.data.duration || ''}
                  onChange={(e) => updateData('duration', parseInt(e.target.value))}
                />
                <Select value={node.data.unit} onValueChange={(value) => updateData('unit', value)}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ms">ms</SelectItem>
                    <SelectItem value="s">s</SelectItem>
                    <SelectItem value="m">m</SelectItem>
                    <SelectItem value="h">h</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )
      
      default:
        return null
    }
  }

  return (
    <div className="p-4 h-full overflow-y-auto">
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Node Properties</h3>
          <Badge variant="outline">{node.type}</Badge>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Label</Label>
            <Input
              value={node.data.label || ''}
              onChange={(e) => updateData('label', e.target.value)}
            />
          </div>

          {renderNodeSpecificFields()}

          <div className="space-y-2">
            <Label>Notes</Label>
            <Textarea
              placeholder="Add notes about this node..."
              value={node.data.notes || ''}
              onChange={(e) => updateData('notes', e.target.value)}
            />
          </div>
        </div>
      </div>
    </div>
  )
}