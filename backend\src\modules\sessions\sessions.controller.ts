import {
  Controller,
  Get,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SessionsService } from './sessions.service';
import { AuthGuard } from '../../common/guards/auth.guard';

@ApiTags('Sessions')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('sessions')
export class SessionsController {
  constructor(private readonly sessionsService: SessionsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all sessions for organization' })
  @ApiResponse({ status: 200, description: 'List of sessions' })
  findAll(@Request() req) {
    return this.sessionsService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get session by ID' })
  @ApiResponse({ status: 200, description: 'Session details' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.sessionsService.findOne(id, req.user.organizationId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete session' })
  @ApiResponse({ status: 200, description: 'Session deleted successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  remove(@Param('id') id: string, @Request() req) {
    return this.sessionsService.remove(id, req.user.organizationId);
  }
}