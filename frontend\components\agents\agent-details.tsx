'use client'

import { useState } from 'react'
import { useMutation, useQueryClient } from 'react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { api } from '@/lib/api'
import { websocketService } from '@/lib/websocket'
import { Bot, Send, MessageCircle, Settings, Activity } from 'lucide-react'
import { useEffect, useRef } from 'react'
import toast from 'react-hot-toast'

interface AgentDetailsProps {
  agent: any
}

export function AgentDetails({ agent }: AgentDetailsProps) {
  const [message, setMessage] = useState('')
  const [currentSession, setCurrentSession] = useState<any>(null)
  const [messages, setMessages] = useState<any[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const queryClient = useQueryClient()

  useEffect(() => {
    // Connect to WebSocket when component mounts
    const socket = websocketService.connect()
    if (socket) {
      setIsConnected(true)
      
      // Listen for message events
      websocketService.on('message-generated', (data) => {
        if (data.sessionId === currentSession?.id) {
          setMessages(prev => [...prev, {
            id: data.messageId,
            role: 'ASSISTANT',
            content: data.content,
            createdAt: new Date().toISOString(),
          }])
        }
      })
    }

    return () => {
      websocketService.disconnect()
      setIsConnected(false)
    }
  }, [currentSession])

  useEffect(() => {
    // Scroll to bottom when new messages are added
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const chatMutation = useMutation(
    (data: { message: string; sessionId?: string }) =>
      api.post(`/agents/${agent.id}/chat`, data),
    {
      onSuccess: (response) => {
        const { sessionId, userMessage, assistantMessage } = response.data
        
        if (!currentSession) {
          setCurrentSession({ id: sessionId })
          websocketService.joinSession(sessionId)
        }
        
        setMessages(prev => [
          ...prev,
          {
            id: userMessage.id,
            role: 'USER',
            content: userMessage.content,
            createdAt: userMessage.createdAt || new Date().toISOString(),
          },
          {
            id: assistantMessage.id,
            role: 'ASSISTANT',
            content: assistantMessage.content,
            createdAt: assistantMessage.createdAt || new Date().toISOString(),
          }
        ])
        
        setMessage('')
      },
      onError: () => {
        toast.error('Failed to send message')
      },
    }
  )

  const handleSendMessage = () => {
    if (!message.trim()) return
    
    chatMutation.mutate({
      message,
      sessionId: currentSession?.id,
    })
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Bot className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold">{agent.name}</h1>
            <p className="text-muted-foreground">{agent.description}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={agent.isActive ? 'default' : 'secondary'}>
            {agent.isActive ? 'Active' : 'Inactive'}
          </Badge>
          <Badge variant={isConnected ? 'default' : 'destructive'}>
            {isConnected ? 'Connected' : 'Disconnected'}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Agent Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Provider</label>
              <p className="font-medium">{agent.provider}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Model</label>
              <p className="font-medium">{agent.model}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Type</label>
              <p className="font-medium">{agent.type}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Temperature</label>
              <p className="font-medium">{agent.temperature}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Max Tokens</label>
              <p className="font-medium">{agent.maxTokens}</p>
            </div>
            {agent.skills && agent.skills.length > 0 && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Skills</label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {agent.skills.map((skill: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Chat Interface */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              Chat with {agent.name}
            </CardTitle>
            <CardDescription>
              Start a conversation with your AI agent
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Messages */}
              <ScrollArea className="h-96 w-full border rounded-md p-4">
                <div className="space-y-4">
                  {messages.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">
                      <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Start a conversation with {agent.name}</p>
                    </div>
                  ) : (
                    messages.map((msg) => (
                      <div
                        key={msg.id}
                        className={`flex ${msg.role === 'USER' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[80%] rounded-lg px-4 py-2 ${
                            msg.role === 'USER'
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted'
                          }`}
                        >
                          <p className="text-sm">{msg.content}</p>
                        </div>
                      </div>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Message Input */}
              <div className="flex gap-2">
                <Input
                  placeholder="Type your message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={chatMutation.isLoading}
                />
                <Button 
                  onClick={handleSendMessage} 
                  disabled={!message.trim() || chatMutation.isLoading}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Sessions */}
      {agent.sessions && agent.sessions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Sessions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {agent.sessions.slice(0, 5).map((session: any) => (
                <div key={session.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{session.name || `Session ${session.id.slice(-8)}`}</p>
                    <p className="text-sm text-muted-foreground">
                      {session.messages?.length || 0} messages
                    </p>
                  </div>
                  <Badge variant={session.status === 'ACTIVE' ? 'default' : 'secondary'}>
                    {session.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}