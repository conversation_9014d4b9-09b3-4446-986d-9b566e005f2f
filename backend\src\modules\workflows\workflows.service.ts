import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';
import { ApixService } from '../apix/apix.service';
import { AgentsService } from '../agents/agents.service';
import { ToolsService } from '../tools/tools.service';
import { CreateWorkflowDto, UpdateWorkflowDto, ExecuteWorkflowDto } from './dto/workflow.dto';

@Injectable()
export class WorkflowsService {
  constructor(
    private prisma: PrismaService,
    private apixService: ApixService,
    private agentsService: AgentsService,
    private toolsService: ToolsService,
  ) {}

  async create(createWorkflowDto: CreateWorkflowDto, organizationId: string) {
    const workflow = await this.prisma.workflow.create({
      data: {
        ...createWorkflowDto,
        organizationId,
      },
      include: {
        nodes: true,
      },
    });

    // Create workflow nodes if they exist in the definition
    if (createWorkflowDto.definition?.nodes) {
      await this.createWorkflowNodes(workflow.id, createWorkflowDto.definition.nodes);
    }

    this.apixService.emitToOrganization(organizationId, 'workflow-created', {
      workflowId: workflow.id,
      name: workflow.name,
    });

    return this.findOne(workflow.id, organizationId);
  }

  async findAll(organizationId: string) {
    return this.prisma.workflow.findMany({
      where: { organizationId },
      include: {
        nodes: {
          include: {
            agent: true,
            tool: true,
          },
        },
        executions: {
          take: 5,
          orderBy: { createdAt: 'desc' },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });
  }

  async findOne(id: string, organizationId: string) {
    const workflow = await this.prisma.workflow.findFirst({
      where: { id, organizationId },
      include: {
        nodes: {
          include: {
            agent: true,
            tool: true,
          },
        },
        executions: {
          include: {
            toolExecutions: true,
          },
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found');
    }

    return workflow;
  }

  async update(id: string, updateWorkflowDto: UpdateWorkflowDto, organizationId: string) {
    const workflow = await this.prisma.workflow.updateMany({
      where: { id, organizationId },
      data: updateWorkflowDto,
    });

    if (workflow.count === 0) {
      throw new NotFoundException('Workflow not found');
    }

    // Update workflow nodes if definition is provided
    if (updateWorkflowDto.definition?.nodes) {
      // Delete existing nodes
      await this.prisma.workflowNode.deleteMany({
        where: { workflowId: id },
      });
      
      // Create new nodes
      await this.createWorkflowNodes(id, updateWorkflowDto.definition.nodes);
    }

    const updatedWorkflow = await this.findOne(id, organizationId);

    this.apixService.emitToOrganization(organizationId, 'workflow-updated', {
      workflowId: id,
      changes: updateWorkflowDto,
    });

    return updatedWorkflow;
  }

  async remove(id: string, organizationId: string) {
    const workflow = await this.prisma.workflow.deleteMany({
      where: { id, organizationId },
    });

    if (workflow.count === 0) {
      throw new NotFoundException('Workflow not found');
    }

    this.apixService.emitToOrganization(organizationId, 'workflow-deleted', {
      workflowId: id,
    });

    return { deleted: true };
  }

  async execute(id: string, executeDto: ExecuteWorkflowDto, organizationId: string, userId: string) {
    const workflow = await this.findOne(id, organizationId);

    const execution = await this.prisma.workflowExecution.create({
      data: {
        workflowId: id,
        input: executeDto.input,
        status: 'RUNNING',
        sessionId: executeDto.sessionId,
      },
    });

    this.apixService.emitToOrganization(organizationId, 'workflow-execution-started', {
      executionId: execution.id,
      workflowId: id,
    });

    try {
      const result = await this.executeWorkflowDefinition(workflow, executeDto.input, execution.id, organizationId, userId);

      const completedExecution = await this.prisma.workflowExecution.update({
        where: { id: execution.id },
        data: {
          output: result,
          status: 'COMPLETED',
          completedAt: new Date(),
        },
      });

      this.apixService.emitToOrganization(organizationId, 'workflow-execution-completed', {
        executionId: execution.id,
        workflowId: id,
        result,
      });

      return completedExecution;
    } catch (error) {
      await this.prisma.workflowExecution.update({
        where: { id: execution.id },
        data: {
          status: 'FAILED',
          error: error.message,
          completedAt: new Date(),
        },
      });

      this.apixService.emitToOrganization(organizationId, 'workflow-execution-failed', {
        executionId: execution.id,
        workflowId: id,
        error: error.message,
      });

      throw error;
    }
  }

  private async createWorkflowNodes(workflowId: string, nodes: any[]) {
    for (const node of nodes) {
      await this.prisma.workflowNode.create({
        data: {
          id: node.id,
          workflowId,
          type: this.mapNodeTypeToEnum(node.type),
          position: node.position,
          config: node.data || {},
          agentId: node.data?.agentId || null,
          toolId: node.data?.toolId || null,
        },
      });
    }
  }

  private mapNodeTypeToEnum(type: string) {
    const typeMap = {
      agent: 'AGENT',
      tool: 'TOOL',
      condition: 'CONDITION',
      trigger: 'TRIGGER',
      output: 'OUTPUT',
      delay: 'CONDITION', // Map delay to condition for now
    };
    return typeMap[type] || 'CONDITION';
  }

  private async executeWorkflowDefinition(workflow: any, input: any, executionId: string, organizationId: string, userId: string) {
    const nodes = workflow.definition?.nodes || [];
    const edges = workflow.definition?.edges || [];
    
    if (nodes.length === 0) {
      throw new Error('Workflow has no nodes to execute');
    }

    // Find the trigger node (entry point)
    const triggerNode = nodes.find((node: any) => node.type === 'trigger');
    if (!triggerNode) {
      throw new Error('Workflow must have a trigger node');
    }

    // Execute workflow starting from trigger
    const context = { input, results: new Map(), variables: new Map() };
    const result = await this.executeNode(triggerNode, context, nodes, edges, organizationId, userId, executionId);

    return {
      input,
      output: result,
      executedNodes: Array.from(context.results.keys()),
      variables: Object.fromEntries(context.variables),
    };
  }

  private async executeNode(node: any, context: any, nodes: any[], edges: any[], organizationId: string, userId: string, executionId: string): Promise<any> {
    this.apixService.emitToOrganization(organizationId, 'workflow-node-executing', {
      executionId,
      nodeId: node.id,
      nodeType: node.type,
    });

    let nodeResult;

    try {
      switch (node.type) {
        case 'trigger':
          nodeResult = context.input;
          break;
        
        case 'agent':
          nodeResult = await this.executeAgentNode(node, context, organizationId, userId);
          break;
        
        case 'tool':
          nodeResult = await this.executeToolNode(node, context, organizationId, executionId);
          break;
        
        case 'condition':
          nodeResult = await this.executeConditionNode(node, context);
          break;
        
        case 'delay':
          nodeResult = await this.executeDelayNode(node, context);
          break;
        
        case 'output':
          nodeResult = await this.executeOutputNode(node, context);
          break;
        
        default:
          throw new Error(`Unsupported node type: ${node.type}`);
      }

      // Store result in context
      context.results.set(node.id, nodeResult);

      this.apixService.emitToOrganization(organizationId, 'workflow-node-completed', {
        executionId,
        nodeId: node.id,
        result: nodeResult,
      });

      // Find and execute next nodes
      const nextEdges = edges.filter((edge: any) => edge.source === node.id);
      
      if (nextEdges.length === 0) {
        // This is an end node
        return nodeResult;
      }

      // Execute all connected nodes
      const nextResults = [];
      for (const edge of nextEdges) {
        const nextNode = nodes.find((n: any) => n.id === edge.target);
        if (nextNode) {
          // For condition nodes, check which path to take
          if (node.type === 'condition' && edge.sourceHandle) {
            const conditionResult = nodeResult.conditionResult;
            if ((edge.sourceHandle === 'true' && !conditionResult) || 
                (edge.sourceHandle === 'false' && conditionResult)) {
              continue; // Skip this path
            }
          }
          
          const nextResult = await this.executeNode(nextNode, context, nodes, edges, organizationId, userId, executionId);
          nextResults.push(nextResult);
        }
      }

      return nextResults.length === 1 ? nextResults[0] : nextResults;

    } catch (error) {
      this.apixService.emitToOrganization(organizationId, 'workflow-node-failed', {
        executionId,
        nodeId: node.id,
        error: error.message,
      });
      throw error;
    }
  }

  private async executeAgentNode(node: any, context: any, organizationId: string, userId: string) {
    const agentId = node.data?.agentId;
    if (!agentId) {
      throw new Error(`Agent node ${node.id} has no agent selected`);
    }

    // Get the message from context or use default
    const message = this.resolveTemplate(node.data?.message || '{{ $json.input }}', context);
    
    const response = await this.agentsService.chat(
      agentId,
      { message: typeof message === 'string' ? message : JSON.stringify(message) },
      organizationId,
      userId
    );

    return {
      agentResponse: response.response,
      sessionId: response.sessionId,
      messageId: response.assistantMessage.id,
    };
  }

  private async executeToolNode(node: any, context: any, organizationId: string, executionId: string) {
    const toolId = node.data?.toolId;
    if (!toolId) {
      throw new Error(`Tool node ${node.id} has no tool selected`);
    }

    // Resolve input mapping
    let toolInput = context.input;
    if (node.data?.inputMapping) {
      try {
        const mapping = JSON.parse(node.data.inputMapping);
        toolInput = this.resolveTemplate(mapping, context);
      } catch (error) {
        console.warn('Failed to parse input mapping, using default input');
      }
    }

    const execution = await this.toolsService.execute(
      toolId,
      { input: toolInput, sessionId: null },
      organizationId
    );

    return {
      toolOutput: execution.output,
      executionId: execution.id,
      status: execution.status,
    };
  }

  private async executeConditionNode(node: any, context: any) {
    const condition = node.data?.condition || 'true';
    
    try {
      // Simple condition evaluation - in production, use a safe expression evaluator
      const result = this.evaluateCondition(condition, context);
      
      return {
        conditionResult: result,
        condition,
        value: result ? (node.data?.trueValue || true) : (node.data?.falseValue || false),
      };
    } catch (error) {
      console.error('Condition evaluation error:', error);
      return {
        conditionResult: false,
        condition,
        error: error.message,
      };
    }
  }

  private async executeDelayNode(node: any, context: any) {
    const duration = node.data?.duration || 1000;
    const unit = node.data?.unit || 'ms';
    
    // Convert to milliseconds
    let delayMs = duration;
    switch (unit) {
      case 's': delayMs = duration * 1000; break;
      case 'm': delayMs = duration * 60 * 1000; break;
      case 'h': delayMs = duration * 60 * 60 * 1000; break;
    }

    // In a real system, you'd want to use a job queue for delays
    await new Promise(resolve => setTimeout(resolve, Math.min(delayMs, 30000))); // Max 30s for demo

    return {
      delayed: true,
      duration: delayMs,
      unit,
    };
  }

  private async executeOutputNode(node: any, context: any) {
    const format = node.data?.format || 'json';
    const template = node.data?.template || '{{ $json }}';
    
    const output = this.resolveTemplate(template, context);
    
    return {
      format,
      output,
      template,
    };
  }

  private resolveTemplate(template: any, context: any): any {
    if (typeof template === 'string') {
      // Simple template resolution - replace {{ $json.path }} with actual values
      let resolved = template;
      
      // Replace {{ $json }} with full context input
      resolved = resolved.replace(/\{\{\s*\$json\s*\}\}/g, JSON.stringify(context.input));
      
      // Replace {{ $json.path }} with specific path values
      const pathMatches = resolved.match(/\{\{\s*\$json\.([^}]+)\s*\}\}/g);
      if (pathMatches) {
        pathMatches.forEach(match => {
          const path = match.replace(/\{\{\s*\$json\.([^}]+)\s*\}\}/, '$1');
          const value = this.getValueByPath(context.input, path);
          resolved = resolved.replace(match, JSON.stringify(value));
        });
      }
      
      // Try to parse as JSON if it looks like JSON
      try {
        return JSON.parse(resolved);
      } catch {
        return resolved;
      }
    } else if (typeof template === 'object' && template !== null) {
      // Recursively resolve object templates
      const resolved = {};
      for (const [key, value] of Object.entries(template)) {
        resolved[key] = this.resolveTemplate(value, context);
      }
      return resolved;
    }
    
    return template;
  }

  private getValueByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private evaluateCondition(condition: string, context: any): boolean {
    // Simple condition evaluation - in production, use a safe expression evaluator like jsonata
    try {
      // Replace template variables
      let resolvedCondition = condition;
      resolvedCondition = resolvedCondition.replace(/input\./g, 'context.input.');
      resolvedCondition = resolvedCondition.replace(/\$json\./g, 'context.input.');
      
      // Create a safe evaluation function
      const func = new Function('context', `return ${resolvedCondition}`);
      return Boolean(func(context));
    } catch (error) {
      console.error('Condition evaluation error:', error);
      return false;
    }
  }
}