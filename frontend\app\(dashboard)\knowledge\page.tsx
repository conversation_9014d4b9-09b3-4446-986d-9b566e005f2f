'use client'

import { useState } from 'react'
import { useQuery } from 'react-query'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { api } from '@/lib/api'
import { Database, Plus, FileText, Upload } from 'lucide-react'

export default function KnowledgePage() {
  const { data: knowledgeBases, isLoading } = useQuery('knowledge', () =>
    api.get('/knowledge').then(res => res.data)
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Knowledge Base</h1>
          <p className="text-muted-foreground">
            Manage documents and knowledge for RAG retrieval
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Knowledge Base
        </Button>
      </div>

      {knowledgeBases?.length === 0 ? (
        <div className="text-center py-12">
          <Database className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">No knowledge bases yet</h3>
          <p className="text-muted-foreground mb-4">
            Create your first knowledge base to store documents for AI retrieval
          </p>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Knowledge Base
          </Button>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {knowledgeBases?.map((kb: any) => (
            <Card key={kb.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">{kb.name}</CardTitle>
                </div>
                <CardDescription>
                  {kb.description || 'No description provided'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Documents:</span>
                    <span className="font-medium">{kb.documents?.length || 0}</span>
                  </div>
                  
                  {kb.documents && kb.documents.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Recent Documents:</h4>
                      <div className="space-y-1">
                        {kb.documents.slice(0, 3).map((doc: any) => (
                          <div key={doc.id} className="flex items-center gap-2 text-sm">
                            <FileText className="h-3 w-3 text-muted-foreground" />
                            <span className="truncate">{doc.name}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div className="flex gap-2">
                    <Button size="sm" className="flex-1">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload
                    </Button>
                    <Button variant="outline" size="sm">
                      Manage
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}