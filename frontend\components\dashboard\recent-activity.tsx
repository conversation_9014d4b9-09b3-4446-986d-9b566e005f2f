import { Badge } from '@/components/ui/badge'
import { formatDistanceToNow } from 'date-fns'

interface RecentActivityProps {
  data: any[]
}

export function RecentActivity({ data }: RecentActivityProps) {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No recent activity</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {data.slice(0, 5).map((execution) => (
        <div key={execution.id} className="flex items-center gap-4">
          <div className="flex-1 space-y-1">
            <p className="text-sm font-medium leading-none">
              {execution.tool?.name || 'Unknown Tool'}
            </p>
            <p className="text-sm text-muted-foreground">
              {execution.status}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={execution.status === 'COMPLETED' ? 'default' : execution.status === 'FAILED' ? 'destructive' : 'secondary'}>
              {execution.status}
            </Badge>
            <p className="text-xs text-muted-foreground">
              {formatDistanceToNow(new Date(execution.createdAt), { addSuffix: true })}
            </p>
          </div>
        </div>
      ))}
    </div>
  )
}