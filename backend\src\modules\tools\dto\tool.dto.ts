import { IsString, IsEnum, IsObject, IsOptional, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ToolType } from '@prisma/client';

export class CreateToolDto {
  @ApiProperty({ example: 'Weather API Tool' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'Fetches current weather data' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ enum: ToolType, example: 'REST_API' })
  @IsEnum(ToolType)
  type: ToolType;

  @ApiProperty({ 
    example: { 
      url: 'https://api.weather.com/current',
      method: 'GET',
      headers: { 'API-Key': 'your-key' }
    }
  })
  @IsObject()
  config: Record<string, any>;

  @ApiProperty({
    example: {
      type: 'object',
      properties: {
        location: { type: 'string', description: 'City name' }
      },
      required: ['location']
    }
  })
  @IsObject()
  inputSchema: Record<string, any>;

  @ApiProperty({
    example: {
      type: 'object',
      properties: {
        temperature: { type: 'number' },
        humidity: { type: 'number' }
      }
    }
  })
  @IsOptional()
  @IsObject()
  outputSchema?: Record<string, any>;

  @ApiProperty({ example: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateToolDto {
  @ApiProperty({ example: 'Updated Tool Name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ example: 'Updated description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ enum: ToolType })
  @IsOptional()
  @IsEnum(ToolType)
  type?: ToolType;

  @ApiProperty()
  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @ApiProperty()
  @IsOptional()
  @IsObject()
  inputSchema?: Record<string, any>;

  @ApiProperty()
  @IsOptional()
  @IsObject()
  outputSchema?: Record<string, any>;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class ExecuteToolDto {
  @ApiProperty({ example: { location: 'New York' } })
  @IsObject()
  input: Record<string, any>;

  @ApiProperty({ example: 'session_123', required: false })
  @IsOptional()
  @IsString()
  sessionId?: string;
}