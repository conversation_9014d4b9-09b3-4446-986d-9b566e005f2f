import { Module } from '@nestjs/common';
import { AgentsService } from './agents.service';
import { AgentsController } from './agents.controller';
import { PrismaModule } from '../../common/prisma/prisma.module';
import { ProvidersModule } from '../providers/providers.module';
import { ApixModule } from '../apix/apix.module';

@Module({
  imports: [PrismaModule, ProvidersModule, ApixModule],
  controllers: [AgentsController],
  providers: [AgentsService],
  exports: [AgentsService],
})
export class AgentsModule {}