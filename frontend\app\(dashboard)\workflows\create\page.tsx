'use client'

import { WorkflowBuilder } from '@/components/workflows/workflow-builder'
import { useRouter } from 'next/navigation'

export default function CreateWorkflowPage() {
  const router = useRouter()

  const handleSave = (workflow: any) => {
    // Workflow created successfully
    router.push('/workflows')
  }

  return (
    <div className="h-screen">
      <WorkflowBuilder onSave={handleSave} />
    </div>
  )
}