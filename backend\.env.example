DATABASE_URL="postgresql://username:password@localhost:5432/synapseai"
REDIS_URL="redis://localhost:6379"
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
FRONTEND_URL="http://localhost:3000"

# AI Provider API Keys - ALL PROVIDERS SUPPORTED
OPENAI_API_KEY="sk-your-openai-api-key"
ANTHROPIC_API_KEY="sk-ant-your-anthropic-api-key"
GROQ_API_KEY="gsk_your-groq-api-key"
MISTRAL_API_KEY="your-mistral-api-key"
GOOGLE_API_KEY="your-google-gemini-api-key"
DEEPSEEK_API_KEY="your-deepseek-api-key"
TOGETHER_API_KEY="your-together-api-key"
PERPLEXITY_API_KEY="pplx-your-perplexity-api-key"

# Server Configuration
PORT=3001
NODE_ENV=development

# Production Security (for production deployment)
CORS_ORIGINS="https://yourdomain.com"
SESSION_SECRET="your-session-secret"
ENCRYPTION_KEY="your-encryption-key-32-chars"

# Optional: Database connection pooling
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# Optional: Redis configuration
REDIS_PASSWORD=""
REDIS_DB=0

# Optional: File storage (for knowledge base documents)
STORAGE_TYPE="local" # or "s3", "gcs"
UPLOAD_MAX_SIZE=10485760 # 10MB

# Optional: Monitoring and logging
LOG_LEVEL="info"
SENTRY_DSN=""

# Optional: Rate limiting per provider (requests per minute)
OPENAI_RATE_LIMIT=60
ANTHROPIC_RATE_LIMIT=50
GROQ_RATE_LIMIT=100
MISTRAL_RATE_LIMIT=60
GOOGLE_RATE_LIMIT=60
DEEPSEEK_RATE_LIMIT=100
TOGETHER_RATE_LIMIT=100
PERPLEXITY_RATE_LIMIT=60