'use client'

import { io, Socket } from 'socket.io-client'

class WebSocketService {
  private socket: Socket | null = null
  private token: string | null = null

  connect() {
    this.token = localStorage.getItem('token')
    
    if (!this.token) {
      console.warn('No auth token found for WebSocket connection')
      return
    }

    const WS_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'
    
    this.socket = io(`${WS_URL}/apix`, {
      auth: {
        token: this.token,
      },
    })

    this.socket.on('connect', () => {
      console.log('Connected to SynapseAI WebSocket')
    })

    this.socket.on('disconnect', () => {
      console.log('Disconnected from SynapseAI WebSocket')
    })

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error)
    })

    return this.socket
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  joinSession(sessionId: string) {
    if (this.socket) {
      this.socket.emit('join-session', { sessionId })
    }
  }

  leaveSession(sessionId: string) {
    if (this.socket) {
      this.socket.emit('leave-session', { sessionId })
    }
  }

  on(event: string, callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on(event, callback)
    }
  }

  off(event: string, callback?: (data: any) => void) {
    if (this.socket) {
      this.socket.off(event, callback)
    }
  }

  emit(event: string, data: any) {
    if (this.socket) {
      this.socket.emit(event, data)
    }
  }
}

export const websocketService = new WebSocketService()